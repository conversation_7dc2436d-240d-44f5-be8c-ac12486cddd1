{"env": {"browser": true, "es6": true, "node": true}, "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["react", "@typescript-eslint"], "rules": {"react/react-in-jsx-scope": "off"}, "globals": {"chrome": "readonly"}, "ignorePatterns": ["watch.js", "dist/**"]}