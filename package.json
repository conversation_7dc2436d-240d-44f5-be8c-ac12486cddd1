{"name": "meta-glasses-video-monitor", "version": "1.0.0", "description": "Monitor your video calls with the Meta Glasses", "license": "MIT", "author": "Devon Crebbin", "scripts": {"build": "run-p build:chrome build:firefox", "build:chrome": "wxt zip", "build:firefox": "wxt -b firefox zip", "dev": "bun dev:chrome", "dev:chrome": "wxt -b chrome", "dev:brave": "wxt -b brave", "dev:firefox": "wxt -b firefox", "lint": "biome check", "lint:fix": "biome check --write", "typecheck": "tsc --noEmit", "postinstall": "wxt prepare", "clean": "rm -rf .wxt node_modules build bun.lockb"}, "type": "module", "dependencies": {"@ai-sdk/openai": "^1.3.22", "@ai-sdk/perplexity": "^1.1.9", "@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/google": "^1.2.19", "@ai-sdk/deepseek": "^0.2.14", "@ai-sdk/xai": "^1.2.16", "@biomejs/biome": "^1.9.4", "@hookform/resolvers": "^5.0.1", "@openpanel/web": "^1.0.1", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tooltip": "^1.2.7", "@t3-oss/env-core": "0.12.0", "@tanstack/react-query": "^5.72.2", "@webext-core/messaging": "^2.2.0", "ai": "^4.3.16", "chrome-ai": "^1.11.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.487.0", "marked": "^15.0.8", "next-themes": "^0.4.6", "npm-run-all": "^4.1.5", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.55.0", "react-router-dom": "^7.5.0", "scrapybara": "^2.2.2", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "vite-plugin-svgr": "^4.3.0", "zod": "^3.24.2", "zustand": "^5.0.5"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.3", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.3", "@total-typescript/ts-reset": "^0.6.1", "@types/bun": "^1.2.9", "@types/chrome": "0.0.313", "@types/node": "22.14.0", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.2", "@wxt-dev/auto-icons": "^1.0.2", "@wxt-dev/module-react": "^1.1.3", "tailwindcss": "^4.1.3", "tw-animate-css": "^1.2.5", "typescript": "^5.8.3", "vitest": "^3.1.1", "wxt": "^0.20.0", "@types/webextension-polyfill": "^0.10.0", "@typescript-eslint/eslint-plugin": "^5.49.0", "@typescript-eslint/parser": "^5.49.0", "autoprefixer": "^10.4.16", "eslint": "^8.32.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "fs-extra": "^11.1.0", "nodemon": "^3.0.3", "postcss": "^8.4.31", "ts-node": "^10.9.1"}, "packageManager": "pnpm@9.12.2+sha512.22721b3a11f81661ae1ec68ce1a7b879425a1ca5b991c975b074ac220b187ce56c708fe5db69f4c962c989452eee76c82877f4ee80f474cebd61ee13461b6228"}