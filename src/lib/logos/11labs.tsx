export const ElevenLabsLogo = () => {
  return (
    <svg
      className="invert"
      xmlns="http://www.w3.org/2000/svg"
      x="0px"
      y="0px"
      width="100"
      height="100"
      viewBox="0 0 48 48"
    >
      <linearGradient
        id="wcu9za2JCL5_IXX0lZWPca_kQSS04V7DmPz_gr1"
        x1="14"
        x2="21"
        y1="24"
        y2="24"
        gradientUnits="userSpaceOnUse"
      >
        <stop offset="0" stop-color="#41474a"></stop>
        <stop offset="1" stop-color="#323538"></stop>
      </linearGradient>
      <path
        fill="url(#wcu9za2JCL5_IXX0lZWPca_kQSS04V7DmPz_gr1)"
        d="M19,41h-3c-1.105,0-2-0.895-2-2V9c0-1.105,0.895-2,2-2h3c1.105,0,2,0.895,2,2v30 C21,40.105,20.105,41,19,41z"
      ></path>
      <linearGradient
        id="wcu9za2JCL5_IXX0lZWPcb_kQSS04V7DmPz_gr2"
        x1="27"
        x2="34"
        y1="24"
        y2="24"
        gradientUnits="userSpaceOnUse"
      >
        <stop offset="0" stop-color="#41474a"></stop>
        <stop offset="1" stop-color="#323538"></stop>
      </linearGradient>
      <path
        fill="url(#wcu9za2JCL5_IXX0lZWPcb_kQSS04V7DmPz_gr2)"
        d="M32,41h-3c-1.105,0-2-0.895-2-2V9 c0-1.105,0.895-2,2-2h3c1.105,0,2,0.895,2,2v30C34,40.105,33.105,41,32,41z"
      ></path>
    </svg>
  );
};
