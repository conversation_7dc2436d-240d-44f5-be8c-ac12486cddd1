{"extends": "./.wxt/tsconfig.json", "compilerOptions": {"lib": ["es2022", "dom", "dom.iterable"], "jsx": "preserve", "esModuleInterop": true, "skipLibCheck": true, "target": "ES2022", "allowJs": true, "resolveJsonModule": true, "moduleDetection": "force", "isolatedModules": true, "incremental": true, "disableSourceOfProjectReferenceRedirect": true, "noUncheckedIndexedAccess": true, "checkJs": true, "module": "Preserve", "moduleResolution": "<PERSON><PERSON><PERSON>", "noEmit": true}, "include": [".wxt/wxt.d.ts", "src", "*.ts", "*.js"], "exclude": ["node_modules"]}